# ***Kesalahan Fatal dalam Email Lamaran Kerja yang Bikin HRD Skip Lamaranmu***

## **Pendahuluan**

<PERSON><PERSON> lalu, saya bertemu seorang teman lama bernama **Rina** di sebuah kafe. Wajahnya terlihat murung sambil menatap layar laptop.

“Aku sudah ngelamar ke 30 perusahaan, tapi belum ada panggilan interview sama se<PERSON>i,” keluhnya.

Pa<PERSON>al, Rina punya **CV yang bagus**, pengalaman organisasi aktif, bahkan IPK di atas 3,5. <PERSON>u saya minta izin melihat salah satu email lamarannya.

Dan ketahuanlah masalahnya: **email pengantar lamaran kerjanya berantakan**. <PERSON> typo, subject kosong, bahkan kadang dia lupa menyebutkan posisi yang dilamar.

Kenyataannya, bukan cuma Rina. Banyak pelamar gagal bukan karena mereka tidak kompeten, tapi karena **kesalahan fatal dalam menulis email lamaran kerja**.

<PERSON><PERSON> art<PERSON> ini, kita akan membagikan 7 kesalahan umum—lengkap dengan contoh nyata—yang sering membuat HRD melewatkan lamaran, serta cara memperbaikinya.

## **1\. Tidak Menulis Subject Email**

Bayangkan inbox HRD yang penuh ratusan email. Kalau subject kosong atau cuma tulis “Lamaran”, HRD bisa langsung skip.

**Contoh salah:**

(tanpa subject)

**Contoh benar:**

Lamaran Kerja – Siti Rahma – Staff Administrasi

## **2\. Email Tanpa Isi (Body Kosong)**

Ini yang dilakukan Rina. CV dan surat lamaran dilampirkan, tapi isi email kosong. HRD tentu menganggap tidak serius.

**Contoh salah:**

(kosong, hanya ada attachment)

**Contoh benar:**

Dengan hormat,  
Saya bermaksud melamar posisi Staff Administrasi di PT Sejahtera Abadi. CV dan surat lamaran terlampir.

## **3\. Salah Menyebut Nama Perusahaan**

Ada kandidat yang copy-paste email, tapi lupa mengganti nama perusahaan. Fatal\! HRD langsung tahu itu template.

**Contoh salah:**

Yth. HRD PT Maju Sentosa (padahal melamar ke PT Makmur Jaya)

**Contoh benar:**

Yth. HRD PT Makmur Jaya

## **4\. Email Terlalu Panjang & Bertele-tele**

Beberapa kandidat menulis body email sepanjang 6 paragraf. HRD tidak punya waktu membacanya.

**Contoh salah (terlalu panjang):**

“Saya lahir di Surabaya tahun 1998, sejak kecil saya suka komputer...”

**Contoh benar (singkat & padat):**

“Saya memiliki pengalaman 2 tahun di bidang IT Support. CV dan portofolio terlampir.”

## **5\. Menggunakan Email Tidak Profesional**

Pakai alamat email aneh bikin HRD ragu.

**Contoh salah:**

ganteng\<EMAIL>  
queenbee\<EMAIL>

**Contoh benar:**

<EMAIL>  
<EMAIL>

## **6\. Lupa Melampirkan CV atau Surat Lamaran**

Pernah dengar cerita kandidat menulis “CV terlampir” tapi tidak ada file sama sekali? Ini sering terjadi, dan bikin kesan ceroboh.

Tips: selalu double-check attachment sebelum klik “*Send*”.

## **7\. Typo & Bahasa Tidak Formal**

Bahasa “alay” atau typo berulang bisa bikin HRD ilfeel.

**Contoh salah:**

“sy mw ngelamar kerja di pT abadi jaya. mohon di pertimbngkan yaa, trims.”

**Contoh benar:**

“Dengan hormat,  
Saya bermaksud melamar posisi Staff Administrasi di PT Abadi Jaya. CV terlampir untuk pertimbangan Bapak/Ibu.”

## **Kisah Rina Setelah Memperbaiki Emailnya**

Setelah saya tunjukkan cara menulis email yang benar, Rina langsung revisi. Dia pakai subject rapi, isi email singkat, dan memastikan semua lampiran sesuai.

Hasilnya? Dua minggu kemudian, dia mengabari:

“Alhamdulillah, aku dipanggil interview di dua perusahaan sekaligus\!”

Artinya jelas: **bukan hanya CV yang menentukan, tapi bagaimana kamu menyajikan diri lewat email.**

## 

## **Gigsta.io: Solusi Anti-Skip untuk Email Lamaran Kerja**

Kalau kamu sering khawatir salah format, coba gunakan [Gigsta.io Email Application](https://www.gigsta.io/email-application).

* Format subject otomatis benar

* Body email rapi & profesional

* Bisa langsung generate sesuai posisi dan perusahaan

Dengan begitu, kamu bisa menghindari 7 kesalahan fatal di atas.

## **FAQ Seputar Kesalahan Email Lamaran Kerja**

**1\. Apakah wajib ada isi email jika sudah melampirkan surat lamaran?**  
 Ya, isi email tetap wajib sebagai pengantar singkat.

**2\. Apakah boleh melamar pakai email Yahoo lama?**  
 Boleh, tapi lebih profesional jika pakai Gmail/Outlook dengan nama asli.

**3\. Apa yang terjadi kalau salah menyebut nama perusahaan?**  
 HRD bisa menganggap tidak serius dan mengabaikan lamaran.

**4\. Apakah panjang email memengaruhi peluang?**  
 Ya, email terlalu panjang membuat HRD malas membaca. Lebih baik singkat tapi jelas.

**5\. Bagaimana cara memastikan tidak lupa lampiran?**  
 Biasakan cek ulang sebelum klik send, atau gunakan fitur reminder di email.

Kesalahan sepele seperti lupa subject, isi email kosong, atau typo bisa jadi alasan utama kenapa HRD melewatkan lamaranmu. Jangan sampai kerja keras membuat CV bagus terbuang percuma hanya karena email tidak profesional.

Ingat kisah Rina: sedikit perbaikan di email bisa jadi pintu masuk menuju interview dan pekerjaan impian.

Kalau mau lebih praktis, coba [Gigsta.io Email Application](https://www.gigsta.io/email-application) untuk membuat email lamaran kerja otomatis, cepat, dan profesional.

FAQ Schema Markup

\<script type="application/ld+json"\>  
{  
  "@context": "https://schema.org",  
  "@type": "FAQPage",  
  "mainEntity": \[  
    {  
      "@type": "Question",  
      "name": "Apakah wajib ada isi email jika sudah melampirkan surat lamaran?",  
      "acceptedAnswer": {  
        "@type": "Answer",  
        "text": "Ya, isi email tetap wajib sebagai pengantar singkat."  
      }  
    },  
    {  
      "@type": "Question",  
      "name": "Apakah boleh melamar pakai email Yahoo lama?",  
      "acceptedAnswer": {  
        "@type": "Answer",  
        "text": "Boleh, tapi lebih profesional jika pakai Gmail/Outlook dengan nama asli."  
      }  
    },  
    {  
      "@type": "Question",  
      "name": "Apa yang terjadi kalau salah menyebut nama perusahaan?",  
      "acceptedAnswer": {  
        "@type": "Answer",  
        "text": "HRD bisa menganggap tidak serius dan mengabaikan lamaran."  
      }  
    },  
    {  
      "@type": "Question",  
      "name": "Apakah panjang email memengaruhi peluang?",  
      "acceptedAnswer": {  
        "@type": "Answer",  
        "text": "Ya, email terlalu panjang membuat HRD malas membaca. Lebih baik singkat tapi jelas."  
      }  
    },  
    {  
      "@type": "Question",  
      "name": "Bagaimana cara memastikan tidak lupa lampiran?",  
      "acceptedAnswer": {  
        "@type": "Answer",  
        "text": "Biasakan cek ulang sebelum klik send, atau gunakan fitur reminder di email."  
      }  
    }  
  \]  
}  
\</script\>

